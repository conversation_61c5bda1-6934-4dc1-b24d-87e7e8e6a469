#ifndef __INI_PARSER_H__
#define __INI_PARSER_H__

#include "stdint.h"
#include "ff.h" // FATFS�ļ�ϵͳ

// Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

// INI������״̬ö��
typedef enum
{
    INI_OK = 0,             // �����ɹ�
    INI_ERROR = 1,          // ��������
    INI_FILE_NOT_FOUND = 2, // �ļ�������
    INI_FORMAT_ERROR = 3,   // ��ʽ����
    INI_VALUE_ERROR = 4     // ��ֵת������
} ini_status_t;

// INI��������ṹ��
typedef struct
{
    float ratio;         // ��Ȳ���
    float limit;         // ��ֵ����
    uint8_t ratio_found; // �Ƿ��ҵ�ratio����
    uint8_t limit_found; // �Ƿ��ҵ�limit����
} ini_config_t;

// INI���������Ľӿ�
ini_status_t ini_parse_file(const char *filename, ini_config_t *config); // ����INI�ļ�
ini_status_t ini_parse_line(const char *line, ini_config_t *config);     // ������������

// ���ߺ���
ini_status_t ini_trim_string(char *str);                     // ȥ���ַ�����β�ո�
ini_status_t ini_parse_float(const char *str, float *value); // ����������

#endif // __INI_PARSER_H__
